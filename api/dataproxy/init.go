package dataproxy

import (
	"assistantdeskgo/api/apis"
	"net/http"
)

const (
	splitComma = ","
)

type AssistantTalkListReq struct {
	GroupIdList []string `json:"groupIdList"`
	CreateName  string   `json:"createName"`
	Content     string   `json:"content"`
	CategoryId  int64    `json:"categoryId"`
	Keyword     string   `json:"keyword"`
	Page        int      `json:"page"`
	PageSize    int      `json:"pageSize"`
	Fields      []string `json:"fields"`
}

type ChatWordTalkSearchReq struct {
	GroupIdList []string `json:"groupIdList"`
	PersonUid   string   `json:"personUid"`
	Content     string   `json:"content"`
	Page        int      `json:"page"`
	PageSize    int      `json:"pageSize"`
	Resource    int64    `json:"resource"`
	CategoryId  int64    `json:"categoryId"`
	Fields      []string `json:"fields"`
}

type GetCourseTaskTiStuDataReq struct {
	CourseId     int64  `json:"courseId" form:"courseId"`
	AssistantUid int64  `form:"assistantUid" json:"assistantUid"`
	TaskId       int64  `form:"taskId" json:"taskId"`
	Fields       string `json:"fields" form:"fields"`
}

type CourseTaskTiStuData struct {
	StudentUid     int64 `json:"student_uid"`
	TaskId         int64 `json:"task_id"`
	Tid            int64 `json:"tid"`
	TidIndex       int   `json:"tid_idx"`
	IsRepeatWrong  int   `json:"is_repeat_wrong"`
	RepeatWrongCnt int   `json:"repeat_wrong_cnt"`
}

type GetCourseTaskTiStuDataRsp struct {
	List  []CourseTaskTiStuData `json:"list"`
	Total int64                 `json:"total"`
}

type ChatWordTalkListRsp struct {
	List  []ChatWordTalkListData `json:"list"`
	Total int64                  `json:"total"`
}
type ChatWordTalkListData struct {
	TalkId string `json:"talk_id"`
}

type ChatWordTalkSearchData struct {
	TalkId       string              `json:"talk_id"`
	Keyword      []string            `json:"keyword"`
	CategoryId   string              `json:"category_id"`
	CategoryName string              `json:"category_name"`
	HighLight    map[string][]string `json:"high_light"`
}

type ChatWordTalkSearchRsp struct {
	List  []ChatWordTalkSearchData `json:"list"`
	Total int64                    `json:"total"`
}

type LuStudentLessonsReq struct {
	StudentUid int64  `form:"studentUid" json:"studentUid"`
	LessonIds  string `form:"lessonIds" json:"lessonIds"`
	Fields     string `form:"fields" json:"fields"`
}

type LuLessonsStudentsReq struct {
	LessonIds   string `form:"lessonIds" json:"lessonIds" binding:"required"`
	StudentUids string `form:"studentUids" json:"studentUids" binding:"required"`
	Fields      string `form:"fields" json:"fields" binding:"required"`
}

// CommonLuRsp es:dataware_idl_common_lesson_student
type CommonLuRsp struct {
	Total int64          `json:"total"`
	List  []CommonLuItem `json:"list"`
}

type CommonLuItem struct {
	CourseID                              int64  `json:"course_id"`
	LessonID                              int64  `json:"lesson_id"`
	StudentUid                            int64  `json:"student_uid"`
	IsInclassTeacherRoomAttend30Minute    int64  `json:"is_inclass_teacher_room_attend_30minute"`
	IsInclassTeacherRoomAttendFinish      int64  `json:"is_inclass_teacher_room_attend_finish"`
	PlaybackParticipateCnt                int64  `json:"playback_participate_cnt"`
	InclassParticipateCnt                 int64  `json:"inclass_participate_cnt"`
	PlaybackRightCnt                      int64  `json:"playback_right_cnt"`
	InclassRightCnt                       int64  `json:"inclass_right_cnt"`
	InclassQuestionCnt                    int64  `json:"inclass_question_cnt"`
	InclassTeacherRoomAttendDuration      int64  `json:"inclass_teacher_room_attend_duration"`
	InclassTeacherRoomTotalPlaybackTimeV1 int64  `json:"inclass_teacher_room_total_playback_time_v1"`
	Exam1                                 string `json:"exam1"`
	Exam7                                 string `json:"exam7"`
}

// LessonStudentActionRsp es:idl_assistant_lesson_student_action
type LessonStudentActionRsp struct {
	Total int64                     `json:"total"`
	List  []LessonStudentActionItem `json:"list"`
}

type LessonStudentActionItem struct {
	CourseID                int64 `json:"course_id"`
	LessonID                int64 `json:"lesson_id"`
	StudentUid              int64 `json:"student_uid"`
	AssistantUid            int64 `json:"assistant_uid"`
	TradeStatus             int64 `json:"trade_status"`
	PlaybackTimeIn7D        int64 `json:"playback_time_in_7d"`
	IsViewFinished          int64 `json:"is_view_finished"`
	HomeworkFirstCorrectCnt int64 `json:"homework_first_correct_cnt"`
	HomeworkFirstRightCnt   int64 `json:"homework_first_right_cnt"`
	ExamAnswer              struct {
		Exam9 *struct {
			ExamType        *string `json:"exam_type"`
			LastCorrectTime *string `json:"last_correct_time"`
			LastSubmitTime  *string `json:"last_submit_time"`
			SubmitNum       *string `json:"submit_num"`
			SubmitTime      *string `json:"submit_time"`
			IsSubmit        *string `json:"is_submit"`
			CorrectLevel    *string `json:"correct_level"`
			SubmitStatus    *string `json:"submit_status"`
			FinishTime      *string `json:"finish_time"`
			ExamTag         *string `json:"exam_tag"`
			CorrectTime     *string `json:"correct_time"`
			AnswerScore     *string `json:"answer_score"`
			IsAmend         *string `json:"is_amend"`
			ParticipateNum  *string `json:"participate_num"`
			TotalNum        *string `json:"total_num"`
			IsFinish        *string `json:"is_finish"`
			RightNum        *string `json:"right_num"`
			ExamID          *string `json:"exam_id"`
		} `json:"exam9"`
	} `json:"exam_answer"`
}

// LessonStudentPointActionRsp es:idl_lesson_student_point_action
type LessonStudentPointActionRsp struct {
	Total int64                          `json:"total"`
	List  []LessonStudentPointActionItem `json:"list"`
}

type LessonStudentPointActionItem struct {
	LessonID               int64 `json:"lesson_id"`
	StudentUid             int64 `json:"student_uid"`
	PointID                int64 `json:"point_id"`
	InclassQuestionCnt     int64 `json:"inclass_question_cnt"`
	InclassRightCnt        int64 `json:"inclass_right_cnt"`
	InclassParticipateCnt  int64 `json:"inclass_participate_cnt"`
	PlaybackRightCnt       int64 `json:"playback_right_cnt"`
	PlaybackParticipateCnt int64 `json:"playback_participate_cnt"`
}

type LuCourseLessonIdsStudentUidsAssistantUidReq struct {
	CourseId     int64  `form:"courseId" json:"courseId"`
	LessonIds    string `form:"lessonIds" json:"lessonIds"`
	StudentUids  string `form:"studentUids" json:"studentUids"`
	AssistantUid int64  `form:"assistantUid" json:"assistantUid"`
	Fields       string `form:"fields" json:"fields"`
}

type LupLessonsStudentUidReq struct {
	LessonIds  string `form:"lessonIds" json:"lessonIds"`
	StudentUid int64  `form:"studentUid" json:"studentUid"`
	Fields     string `form:"fields" json:"fields"`
}

type LupLessonsStudentUidsReq struct {
	LessonIds   string `form:"lessonIds" json:"lessonIds"`
	StudentUids string `form:"studentUids" json:"studentUids"`
	Fields      string `form:"fields" json:"fields"`
}

type LupLessonsStudentUidsExamTypeReq struct {
	LessonIds   string `form:"lessonIds" json:"lessonIds"`
	StudentUids string `form:"studentUids" json:"studentUids"`
	ExamType    int    `form:"examType" json:"examType"`
	Fields      string `form:"fields" json:"fields"`
}

type LessonStudentPointExamActionRsp struct {
	Total int64                        `json:"total"`
	List  []LessonStudentPointExamItem `json:"list"`
}

type LessonStudentPointExamItem struct {
	LessonID       int64 `json:"lesson_id"`
	StudentUid     int64 `json:"student_uid"`
	PointID        int64 `json:"point_id"`
	ExamType       int   `json:"exam_type"`
	ExamID         int64 `json:"exam_id"`
	FirstSubmitCnt int   `json:"first_submit_cnt"`
	FirstRightCnt  int   `json:"first_right_cnt"`
}

type LpCommonPointLessonIdsReq struct {
	LessonIds string `form:"lessonIds" json:"lessonIds"`
	Fields    string `form:"fields" json:"fields"`
}

type LpCommonPointLessonIdsRsp struct {
	Total int64                     `json:"total"`
	List  []LpCommonPointActionItem `json:"list"`
}

type LpCommonPointActionItem struct {
	LessonID           int64 `json:"lesson_id"`
	PointID            int64 `json:"point_id"`
	InclassQuestionCnt int64 `json:"inclass_question_cnt"`
}

type StaffInfoListReq struct {
	StaffUid          []int64  `json:"staffUid,omitempty"`          //职员真人id
	StaffName         []string `json:"staffName,omitempty"`         //职员真人名
	StaffHrPosition   []int64  `json:"staffHrPosition,omitempty"`   //人员岗位
	StaffType         []int64  `json:"staffType,omitempty"`         //人员类型
	StaffAscription   []int64  `json:"staffAscription,omitempty"`   //人员归属
	StaffStatus       []int64  `json:"staffStatus,omitempty"`       //人员状态
	ProductLine       []int64  `json:"productLine,omitempty"`       //业务线
	GroupId           []int64  `json:"groupId,omitempty"`           //组织id
	StaffCity         []int64  `json:"staffCity,omitempty"`         //城市
	StaffGrade        []int64  `json:"staffGrade,omitempty"`        //年级
	StaffGradeLevel   []int64  `json:"staffGradeLevel,omitempty"`   //学部
	StaffSubject      []int64  `json:"staffSubject,omitempty"`      //学科
	NeedGroupChildren int64    `json:"needGroupChildren,omitempty"` //是否需要查指定组织全部下级组织数据
	PositionId        []int64  `json:"positionId,omitempty"`        //岗位id
	RnSize            int64    `json:"rnSize,omitempty"`            //一万条以下全量返回，一万条以上返回10000条，和游标offset
	Offset            string   `json:"offset,omitempty"`            //分页游标
}

type StaffInfoListRsp struct {
	List   []StaffInfo `json:"list"`
	Total  int64       `json:"total"`
	Offset string      `json:"offset"`
}

type StaffInfo struct {
	StaffUid        int64          `json:"staffUid"`
	StaffName       string         `json:"staffName"`
	StaffCity       []int64        `json:"staffCity"`
	StaffGrade      []int64        `json:"staffGrade"`
	StaffGradeLevel []int64        `json:"staffGradeLevel"`
	StaffSubject    []int64        `json:"staffSubject"`
	Organization    []Organization `json:"organization"`
	Device          []DeviceInfo   `json:"device"`
}

type Organization struct {
	ProductLine     int64       `json:"productLine"`
	ProductLineName string      `json:"productLineName"`
	GroupId         int64       `json:"groupId"`
	GroupName       string      `json:"groupName"`
	GroupPaths      []GroupPath `json:"groupPaths"`
	PositionId      int64       `json:"positionId"`
	PositionName    string      `json:"positionName"`
}

type GroupPath struct {
	GroupId   int64  `json:"groupId"`
	GroupName string `json:"groupName"`
}

type DeviceInfo struct {
	DeviceUid int64 `json:"deviceUid"`
}

type GetLeadsAdsBaseModelReq struct {
	CourseId    int64  `form:"courseId"`    // 课程ID
	StudentUids string `form:"studentUids"` // 用户ID
	Date        int    `form:"date"`        // 计算日期
	Fields      string `form:"fields"`      // 需要返回的字段，多个字段用逗号分隔
}

type GetLeadsAdsBaseModelResp struct {
	List []GetLeadsAdsBaseModel `json:"list"`
}

type GetLeadsAdsBaseModel struct {
	Date        int64   `json:"date" mapstructure:"date"`                 // 计算日期
	TransScore  float64 `json:"trans_score" mapstructure:"trans_score"`   // 转化意愿分数
	TransLevel  int     `json:"trans_level" mapstructure:"trans_level"`   // 转化等级：0新，1高，2中，3低
	WxAddTime   int64   `json:"wx_add_time" mapstructure:"wx_add_time"`   // 加微时间
	MdcTime     int64   `json:"mdc_time" mapstructure:"mdc_time"`         // 摸底测时间
	XzkTime     int64   `json:"xzk_time" mapstructure:"xzk_time"`         // 1v1出镜时间
	InclassTime int64   `json:"inclass_time" mapstructure:"inclass_time"` // 到课时间
	Features    string  `json:"features" mapstructure:"features"`         // 模型特征
}

// GetLpcObjLeadsFeatureByCourseStudentReq 获取LPC对象线索特征请求
type GetLpcObjLeadsFeatureByCourseStudentReq struct {
	CourseId    int64  `form:"courseId"`    // 课程ID
	StudentUids string `form:"studentUids"` // 学生ID列表，用逗号分隔
	Fields      string `form:"fields"`      // 需要返回的字段，多个字段用逗号分隔
}

// GetLpcObjLeadsFeatureByCourseStudentResp 获取LPC对象线索特征响应
type GetLpcObjLeadsFeatureByCourseStudentResp struct {
	List []LpcObjLeadsFeatureItem `json:"list"`
}

// LpcObjLeadsFeatureItem LPC对象线索特征数据项
type LpcObjLeadsFeatureItem struct {
	LeadsId                            int64   `json:"leads_id" mapstructure:"leads_id"`                                                             // 线索Id
	StudentUid                         int64   `json:"student_uid" mapstructure:"student_uid"`                                                       // 学号ID
	CourseId                           int64   `json:"course_id" mapstructure:"course_id"`                                                           // 课程Id
	LpcUid                             int64   `json:"lpc_uid" mapstructure:"lpc_uid"`                                                               // LPCUid
	City                               string  `json:"city" mapstructure:"city"`                                                                     // 城市
	CityLevel                          int64   `json:"city_level" mapstructure:"city_level"`                                                         // 城市等级
	ResidenceProvinceGraduateCoverRate float64 `json:"residence_province_graduate_cover_rate" mapstructure:"residence_province_graduate_cover_rate"` // 常住地省份本科覆盖率
	MobileBrand                        string  `json:"mobile_brand" mapstructure:"mobile_brand"`                                                     // 手机品牌
	YikeLastActTime                    int64   `json:"yike_last_act_time" mapstructure:"yike_last_act_time"`                                         // 用户最后活跃app最近活跃时间
	TotalLpcTradeCnt                   int64   `json:"total_lpc_trade_cnt" mapstructure:"total_lpc_trade_cnt"`                                       // 用户访次总计单数
	TotalCourseTradeCnt                int64   `json:"total_course_trade_cnt" mapstructure:"total_course_trade_cnt"`                                 // 用户课程总计单数
	TotalTradeAmt                      int64   `json:"total_trade_amt" mapstructure:"total_trade_amt"`                                               // 总订单金额
	LeadsUserLabel                     string  `json:"leads_user_label" mapstructure:"leads_user_label"`                                             // 当前用户用户标签
	LeadsL1ChannelName                 string  `json:"leads_l1_channel_name" mapstructure:"leads_l1_channel_name"`                                   // 线索一级渠道名称
	LeadsL2ChannelName                 string  `json:"leads_l2_channel_name" mapstructure:"leads_l2_channel_name"`                                   // 线索二级渠道名称
	LeadsL3ChannelName                 string  `json:"leads_l3_channel_name" mapstructure:"leads_l3_channel_name"`                                   // 线索三级渠道名称
	LeadsL4ChannelName                 string  `json:"leads_l4_channel_name" mapstructure:"leads_l4_channel_name"`                                   // 线索四级渠道名称
	LeadsL5ChannelName                 string  `json:"leads_l5_channel_name" mapstructure:"leads_l5_channel_name"`                                   // 线索五级渠道名称
	ChannelBusinessLineName            string  `json:"channel_business_line_name" mapstructure:"channel_business_line_name"`                         // 渠道业务线名称
	IsInitiativeAdd                    int64   `json:"is_initiative_add" mapstructure:"is_initiative_add"`                                           // 是否主动加微
	IsActiveClaim                      int64   `json:"is_active_claim" mapstructure:"is_active_claim"`                                               // 是否主动核销
	UserWechatCnt                      int64   `json:"user_wechat_cnt" mapstructure:"user_wechat_cnt"`                                               // 用户微信群数
	IsModiTestFinished                 int64   `json:"is_modi_test_finished" mapstructure:"is_modi_test_finished"`                                   // 是否完成测评期课程
	IsAppointmentBooked                int64   `json:"is_appointment_booked" mapstructure:"is_appointment_booked"`                                   // 是否预约出单
	AttendCoreCourseLessonNum          int64   `json:"attend_core_course_lesson_num" mapstructure:"attend_core_course_lesson_num"`                   // 核心课程到课节数
	FinishCoreCourseLessonNum          int64   `json:"finish_core_course_lesson_num" mapstructure:"finish_core_course_lesson_num"`                   // 核心课程完课节数
}

const PATH_DATAPROXY_CHATWORD_TALK_LIST = "/dataproxy/chatword/talk-list"
const PATH_DATAPROXY_CHATWORD_TALK_SEARCH = "/dataproxy/chatword/talk-search"
const PATH_DATAPROXY_EXERCISE_NOTE_TI_STU_DATA = "/dataproxy/exercisenote/get-course-ti-stu-data"

const PATH_COMMON_LU_STUDENT_LESSONS = "/dataproxy/lu/get-common-list-by-student-lessons"
const PATH_COMMON_LESSONS_STUDENTS = "/dataproxy/lu/get-common-list-by-lessons-students"
const PATH_LU_COURSE_LESSONIDS_STUDENTUIDS_ASSISTANTUID = "/dataproxy/lu/getListByCourseIdLessonIdsStudentUidsAssistantUid"
const PATH_LUP_LESSONIDS_STUDENTUID = "/dataproxy/lup/getListByLessonIdsStudentUid"
const PATH_LUP_LESSONIDS_STUDENTUIDS = "/dataproxy/lup/getListByLessonIdsStudentUids"
const PATH_LUP_LESSONIDS_STUDENTUIDS_EXAMTYPE = "/dataproxy/lupe/getListByLessonIdsStudentUidsExamType"
const PATH_LP_COMMON_POINT_LESSON_IDS = "/dataproxy/lp/getCommonPointByLessonIds"
const PATH_DATAPROXY_STAFF_INFO_LIST = "/dataproxy/userprofile/getPersonList"
const PATH_GET_LEADS_ADS_BASE_MODEL = "/dataproxy/cu/get-leads-ads-base-model-by-course"
const PATH_GET_LPC_OBJ_LEADS_FEATURE_BY_COURSE_STUDENT = "/dataproxy/cu/get-lpc-obj-leads-feature-by-course-student"

func init() {
	apis.Register(PATH_DATAPROXY_CHATWORD_TALK_LIST, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  AssistantTalkListReq{},
		Encoder:  apis.EncoderJson,
		Response: []ChatWordTalkListRsp{},
	})
	apis.Register(PATH_DATAPROXY_CHATWORD_TALK_SEARCH, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  ChatWordTalkSearchReq{},
		Encoder:  apis.EncoderJson,
		Response: []ChatWordTalkSearchRsp{},
	})
	apis.Register(PATH_DATAPROXY_EXERCISE_NOTE_TI_STU_DATA, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetCourseTaskTiStuDataReq{},
		Encoder:  apis.EncoderForm,
		Response: []GetCourseTaskTiStuDataRsp{},
	})
	apis.Register(ApiGetLpcListByStudentsLessons, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetLpcListByStudentsLessonsReq{},
		Encoder:  apis.EncoderForm,
		Response: GetLpcListByStudentsLessonsResp{},
	})
	apis.Register(apiGetLearningReportByStudentCourse, apis.ApiConfig{
		Method:   http.MethodGet,
		Request:  GetLearningReportByStudentCourseReq{},
		Encoder:  apis.EncoderForm,
		Response: GetLearningReportByStudentCourseResp{},
	})
	apis.Register(apiClueList, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  ClueListReq{},
		Encoder:  apis.EncoderJson,
		Response: ClueListResp{},
	})
	apis.Register(PathRecordListUrl, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  RecordListReq{},
		Encoder:  apis.EncoderJson,
		Response: RecordListResp{},
	})
	apis.Register(apiGetUData, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetUDataReq{},
		Encoder:  apis.EncoderForm,
		Response: GetUDataResp{},
	})
	apis.Register(PATH_COMMON_LU_STUDENT_LESSONS, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  LuStudentLessonsReq{},
		Encoder:  apis.EncoderForm,
		Response: []CommonLuRsp{},
	})
	apis.Register(PATH_COMMON_LESSONS_STUDENTS, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  LuLessonsStudentsReq{},
		Encoder:  apis.EncoderForm,
		Response: []CommonLuRsp{},
	})
	apis.Register(PATH_LU_COURSE_LESSONIDS_STUDENTUIDS_ASSISTANTUID, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  LuCourseLessonIdsStudentUidsAssistantUidReq{},
		Encoder:  apis.EncoderForm,
		Response: []LessonStudentActionRsp{},
	})
	apis.Register(PATH_LUP_LESSONIDS_STUDENTUID, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  LupLessonsStudentUidReq{},
		Encoder:  apis.EncoderForm,
		Response: []LessonStudentPointActionRsp{},
	})
	apis.Register(PATH_LUP_LESSONIDS_STUDENTUIDS, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  LupLessonsStudentUidsReq{},
		Encoder:  apis.EncoderForm,
		Response: []LessonStudentPointActionRsp{},
	})
	apis.Register(PATH_LUP_LESSONIDS_STUDENTUIDS_EXAMTYPE, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  LupLessonsStudentUidsExamTypeReq{},
		Encoder:  apis.EncoderForm,
		Response: []LessonStudentPointExamActionRsp{},
	})
	apis.Register(PATH_LP_COMMON_POINT_LESSON_IDS, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  LpCommonPointLessonIdsReq{},
		Encoder:  apis.EncoderForm,
		Response: LpCommonPointLessonIdsRsp{},
	})
	apis.Register(UrlGetLastAppLoginInfo, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetLastAppLoginInfoReq{},
		Encoder:  apis.EncoderJson,
		Response: GetLastAppLoginInfoRsp{},
	})
	apis.Register(PATH_DATAPROXY_STAFF_INFO_LIST, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  StaffInfoListReq{},
		Encoder:  apis.EncoderJson,
		Response: []StaffInfoListRsp{},
	})
	apis.Register(PATH_GET_LEADS_ADS_BASE_MODEL, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetLeadsAdsBaseModelReq{},
		Encoder:  apis.EncoderForm,
		Response: []GetLeadsAdsBaseModelResp{},
	})
	apis.Register(PATH_GET_LPC_OBJ_LEADS_FEATURE_BY_COURSE_STUDENT, apis.ApiConfig{
		Method:   http.MethodPost,
		Request:  GetLpcObjLeadsFeatureByCourseStudentReq{},
		Encoder:  apis.EncoderJson,
		Response: GetLpcObjLeadsFeatureByCourseStudentResp{},
	})
}
