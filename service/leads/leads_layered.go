package leads

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/dto/dtoleads"
	"assistantdeskgo/utils"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

// 常量
const (
	dateFormatYYYYMMDD    = "20060102"
	dateFormatMMDD        = "01/02"
	dateFormatChineseMMDD = "01月02日"
)

// GetLeadsLayerDetailInfo retrieves layered detail information for a specific course and student.
func GetLeadsLayerDetailInfo(ctx *gin.Context, req dtoleads.GetLeadsLayerDetailInfoReq) (rsp dtoleads.GetLeadsLayerDetailInfoRsp, err error) {
	rsp.PurchaseIntentions = make([]dtoleads.PurchaseIntention, 0)
	rsp.Milestones = make([]dtoleads.Milestone, 0)

	result, err := dataproxy.GetLeadsAdsBaseModelByCourse(ctx, dataproxy.GetLeadsAdsBaseModelReq{
		CourseId:    req.CourseId,
		StudentUids: cast.ToString(req.StudentUid),
		Fields:      strings.Join([]string{"wx_add_time", "mdc_time", "xzk_time", "inclass_time", "trans_score", "trans_level", "date"}, ","),
	})
	if err != nil {
		return rsp, err
	}
	if len(result.List) == 0 {
		return rsp, errors.New("no models found for the given course and user")
	}

	models := result.List
	sort.Slice(models, func(i, j int) bool {
		return models[i].Date > models[j].Date
	})
	latestModel := models[0]

	rsp.Milestones = buildMilestones(ctx, latestModel)
	rsp.PurchaseIntentions = buildPurchaseIntentions(ctx, getModelsByAllocTime(ctx, models, req.AllocTime))
	rsp.RefreshTime = formatDate(latestModel.Date, dateFormatChineseMMDD)
	rsp.TransLevel = getTransLevelDesc(latestModel.TransLevel)

	rsp.Features, err = buildFeatures(ctx, req.CourseId, req.StudentUid)
	if err != nil {
		return rsp, err
	}

	return rsp, nil
}

// buildFeatures constructs a sorted list of features from the model
// 根据 LeadsFeatureConfig 配置构建特征列表，严格按照配置的顺序和内容展示
func buildFeatures(ctx *gin.Context, courseId, studentUid int64) ([]dtoleads.Feature, error) {
	features := make([]dtoleads.Feature, 0)

	// 获取需要展示的特征配置
	LeadsFeatureConfig, err := GetLeadsFeatureConfig(ctx)
	if err != nil || len(LeadsFeatureConfig) == 0 {
		zlog.Warnf(ctx, "LeadsFeatureConfig found no config")
		return features, nil // 如果没有配置，返回空切片
	}

	// 获取学生特征数据
	featuresList, err := dataproxy.GetLpcObjLeadsFeatureByCourseStudent(ctx, dataproxy.GetLpcObjLeadsFeatureByCourseStudentReq{
		CourseId:    courseId,
		StudentUids: cast.ToString(studentUid),
		Fields:      "all",
	})
	if err != nil {
		return nil, err
	}

	// 如果没有找到学生数据，返回空切片而不是错误
	if len(featuresList.List) == 0 {
		return features, nil
	}

	// 将学生数据转换为 map，便于通过字段名动态获取值
	studentData := featuresList.List[0]
	dataMap := utils.StructToMap(&studentData, "mapstructure")

	// 按照 LeadsFeatureConfig 的顺序构建特征列表
	for i, config := range LeadsFeatureConfig {
		// 检查配置的字段名是否在数据中存在
		if value, exists := dataMap[config.Name]; exists && value != nil {
			// 将值转换为字符串
			valueStr := fmt.Sprintf("%v", value)

			feature := dtoleads.Feature{
				Name:   config.Name,
				Desc:   config.Desc,
				Value:  valueStr,
				Weight: len(LeadsFeatureConfig) - i, // 权重按配置顺序递减，保证顺序
			}
			features = append(features, feature)
		}
	}

	return features, nil
}

// MilestoneConfig defines the mapping for milestone fields.
type MilestoneConfig struct {
	Timestamp int64
	Desc      string
}

// buildMilestones constructs a list of milestones based on model timestamps.
func buildMilestones(ctx *gin.Context, model dataproxy.GetLeadsAdsBaseModel) []dtoleads.Milestone {
	configs := []MilestoneConfig{
		{model.WxAddTime, "加微时间"},
		{model.MdcTime, "摸底测时间"},
		{model.XzkTime, "1V1出镜时间"},
		{model.InclassTime, "到课时间"},
	}

	milestones := make([]dtoleads.Milestone, 0, len(configs))
	for _, cfg := range configs {
		if cfg.Timestamp > 0 {
			milestones = append(milestones, dtoleads.Milestone{
				Date: formatTimestampToString(cfg.Timestamp, dateFormatMMDD),
				Desc: cfg.Desc,
			})
		}
	}

	return milestones
}

// buildPurchaseIntentions constructs a list of purchase intentions sorted by date.
func buildPurchaseIntentions(ctx *gin.Context, models []dataproxy.GetLeadsAdsBaseModel) []dtoleads.PurchaseIntention {
	if len(models) == 0 {
		return nil
	}

	// Pre-allocate with estimated capacity
	intentions := make([]dtoleads.PurchaseIntention, 0, len(models))

	// Sort models by date in ascending order
	sortedModels := make([]dataproxy.GetLeadsAdsBaseModel, len(models))
	copy(sortedModels, models)
	sort.Slice(sortedModels, func(i, j int) bool {
		return sortedModels[i].Date < sortedModels[j].Date
	})

	for _, model := range sortedModels {
		intentions = append(intentions, dtoleads.PurchaseIntention{
			Date:  formatDate(model.Date, dateFormatMMDD),
			Score: model.TransScore,
		})
	}

	return intentions
}

// getModelsByAllocTime filters models within the date range around allocTime.
func getModelsByAllocTime(ctx *gin.Context, models []dataproxy.GetLeadsAdsBaseModel, allocTime int64) []dataproxy.GetLeadsAdsBaseModel {
	if allocTime <= 0 {
		return nil
	}

	// 这里的时间范围是例子分配时间的当天和后14天
	startDate := time.Unix(allocTime, 0)
	endDate := time.Unix(allocTime, 0).AddDate(0, 0, 14)
	startDateInt := formatTimeToInt(startDate, dateFormatYYYYMMDD)
	endDateInt := formatTimeToInt(endDate, dateFormatYYYYMMDD)

	// Pre-allocate with estimated capacity
	filtered := make([]dataproxy.GetLeadsAdsBaseModel, 0, len(models))
	for _, model := range models {
		if model.Date >= startDateInt && model.Date <= endDateInt {
			filtered = append(filtered, model)
		}
	}

	return filtered
}

// formatTimestampToString formats a Unix timestamp to the specified format.
func formatTimestampToString(timestamp int64, format string) string {
	if timestamp <= 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format(format)
}

// formatTimeToInt formats a time.Time to an integer date (e.g., 20230101).
func formatTimeToInt(t time.Time, format string) int64 {
	dateStr := t.Format(format)
	return cast.ToInt64(dateStr)
}

func getTransLevelDesc(transLevel int) string {
	switch transLevel {
	case 0:
		return "新"
	case 1:
		return "高"
	case 2:
		return "中"
	case 3:
		return "低"
	default:
		return "新"
	}
}

func formatDate(dateInt int64, format string) string {
	dateStr := cast.ToString(dateInt)
	if len(dateStr) != 8 {
		return ""
	}

	t, err := time.Parse(dateFormatYYYYMMDD, dateStr)
	if err != nil {
		return ""
	}
	return t.Format(format)
}
