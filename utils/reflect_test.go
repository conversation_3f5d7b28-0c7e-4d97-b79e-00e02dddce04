package utils

import (
	"reflect"
	"sync"
	"testing"
)

// 测试用的结构体定义

// TestStructAllFields - 所有字段都有指定标签
type TestStructAllFields struct {
	Name  string `json:"name" db:"user_name"`
	Age   int    `json:"age" db:"user_age"`
	Email string `json:"email" db:"user_email"`
}

// TestStructPartialFields - 部分字段有指定标签
type TestStructPartialFields struct {
	ID       int64  `json:"id" db:"user_id"`
	Name     string `json:"name"`
	Password string // 没有标签
	Status   int    `db:"status"`
}

// TestStructNoFields - 没有任何字段有指定标签
type TestStructNoFields struct {
	Field1 string `xml:"field1"`
	Field2 int    `yaml:"field2"`
	Field3 bool   `toml:"field3"`
}

// TestStructEmpty - 空结构体
type TestStructEmpty struct{}

// TestStructWithAnonymous - 包含匿名字段的结构体
type TestStructWithAnonymous struct {
	TestStructAllFields        // 匿名字段，应该被跳过
	Extra               string `json:"extra"`
}

// TestStructWithIgnoredTag - 包含标签值为 "-" 的字段
type TestStructWithIgnoredTag struct {
	PublicField  string `json:"public"`
	IgnoredField string `json:"-"`
	EmptyTag     string `json:""`
}

// TestGetStructFieldsStrByTag_ValidStruct 测试正常情况
func TestGetStructFieldsStrByTag_ValidStruct(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		tagName  string
		expected string
	}{
		{
			name:     "所有字段都有json标签",
			input:    TestStructAllFields{},
			tagName:  "json",
			expected: "name,age,email",
		},
		{
			name:     "所有字段都有db标签",
			input:    TestStructAllFields{},
			tagName:  "db",
			expected: "user_name,user_age,user_email",
		},
		{
			name:     "部分字段有json标签",
			input:    TestStructPartialFields{},
			tagName:  "json",
			expected: "id,name",
		},
		{
			name:     "部分字段有db标签",
			input:    TestStructPartialFields{},
			tagName:  "db",
			expected: "user_id,status",
		},
		{
			name:     "结构体指针",
			input:    &TestStructAllFields{},
			tagName:  "json",
			expected: "name,age,email",
		},
		{
			name:     "匿名字段被跳过",
			input:    TestStructWithAnonymous{},
			tagName:  "json",
			expected: "extra",
		},
		{
			name:     "忽略标签值为-的字段",
			input:    TestStructWithIgnoredTag{},
			tagName:  "json",
			expected: "public",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetStructFieldsStrByTag(tt.input, tt.tagName)
			if err != nil {
				t.Errorf("GetStructFieldsStrByTag() error = %v, wantErr false", err)
				return
			}
			if result != tt.expected {
				t.Errorf("GetStructFieldsStrByTag() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestGetStructFieldsStrByTag_EmptyStruct 测试空结构体
func TestGetStructFieldsStrByTag_EmptyStruct(t *testing.T) {
	result, err := GetStructFieldsStrByTag(TestStructEmpty{}, "json")
	if err != nil {
		t.Errorf("GetStructFieldsStrByTag() error = %v, wantErr false", err)
		return
	}
	if result != "" {
		t.Errorf("GetStructFieldsStrByTag() = %v, want empty string", result)
	}
}

// TestGetStructFieldsStrByTag_NoMatchingTags 测试没有匹配标签
func TestGetStructFieldsStrByTag_NoMatchingTags(t *testing.T) {
	tests := []struct {
		name    string
		input   interface{}
		tagName string
	}{
		{
			name:    "不存在的标签名称",
			input:   TestStructAllFields{},
			tagName: "nonexistent",
		},
		{
			name:    "结构体没有任何指定标签",
			input:   TestStructNoFields{},
			tagName: "json",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetStructFieldsStrByTag(tt.input, tt.tagName)
			if err != nil {
				t.Errorf("GetStructFieldsStrByTag() error = %v, wantErr false", err)
				return
			}
			if result != "" {
				t.Errorf("GetStructFieldsStrByTag() = %v, want empty string", result)
			}
		})
	}
}

// TestGetStructFieldsStrByTag_ErrorCases 测试错误情况
func TestGetStructFieldsStrByTag_ErrorCases(t *testing.T) {
	tests := []struct {
		name        string
		input       interface{}
		tagName     string
		expectedErr string
	}{
		{
			name:        "nil输入",
			input:       nil,
			tagName:     "json",
			expectedErr: "结构体类型不能为空",
		},
		{
			name:        "空标签名称",
			input:       TestStructAllFields{},
			tagName:     "",
			expectedErr: "标签名称不能为空",
		},
		{
			name:        "基本类型int",
			input:       42,
			tagName:     "json",
			expectedErr: "参数必须是结构体类型",
		},
		{
			name:        "基本类型string",
			input:       "hello",
			tagName:     "json",
			expectedErr: "参数必须是结构体类型",
		},
		{
			name:        "切片类型",
			input:       []int{1, 2, 3},
			tagName:     "json",
			expectedErr: "参数必须是结构体类型",
		},
		{
			name:        "映射类型",
			input:       map[string]int{"a": 1},
			tagName:     "json",
			expectedErr: "参数必须是结构体类型",
		},
		{
			name:        "指向基本类型的指针",
			input:       new(int),
			tagName:     "json",
			expectedErr: "参数必须是结构体类型",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetStructFieldsStrByTag(tt.input, tt.tagName)
			if err == nil {
				t.Errorf("GetStructFieldsStrByTag() error = nil, wantErr true")
				return
			}
			if err.Error() != tt.expectedErr {
				t.Errorf("GetStructFieldsStrByTag() error = %v, want %v", err.Error(), tt.expectedErr)
			}
			if result != "" {
				t.Errorf("GetStructFieldsStrByTag() = %v, want empty string", result)
			}
		})
	}
}

// TestGetStructFieldsByTag 测试底层函数返回切片
func TestGetStructFieldsByTag(t *testing.T) {
	tests := []struct {
		name     string
		input    interface{}
		tagName  string
		expected []string
	}{
		{
			name:     "返回字符串切片",
			input:    TestStructAllFields{},
			tagName:  "json",
			expected: []string{"name", "age", "email"},
		},
		{
			name:     "空结果",
			input:    TestStructEmpty{},
			tagName:  "json",
			expected: []string{},
		},
		{
			name:     "部分字段",
			input:    TestStructPartialFields{},
			tagName:  "db",
			expected: []string{"user_id", "status"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetStructFieldsByTag(tt.input, tt.tagName)
			if err != nil {
				t.Errorf("GetStructFieldsByTag() error = %v, wantErr false", err)
				return
			}
			if len(result) != len(tt.expected) {
				t.Errorf("GetStructFieldsByTag() length = %v, want %v", len(result), len(tt.expected))
				return
			}
			for i, v := range result {
				if v != tt.expected[i] {
					t.Errorf("GetStructFieldsByTag()[%d] = %v, want %v", i, v, tt.expected[i])
				}
			}
		})
	}
}

// TestGetStructFieldsStrByTag_Cache 测试缓存机制
func TestGetStructFieldsStrByTag_Cache(t *testing.T) {
	// 清空缓存以确保测试的准确性
	structFieldsCacheMutex.Lock()
	originalCache := structFieldsCache
	structFieldsCache = make(map[string][]string)
	structFieldsCacheMutex.Unlock()

	// 测试结束后恢复原缓存
	defer func() {
		structFieldsCacheMutex.Lock()
		structFieldsCache = originalCache
		structFieldsCacheMutex.Unlock()
	}()

	input := TestStructAllFields{}
	tagName := "json"
	expected := "name,age,email"

	// 第一次调用
	result1, err1 := GetStructFieldsStrByTag(input, tagName)
	if err1 != nil {
		t.Errorf("First call error = %v, wantErr false", err1)
		return
	}
	if result1 != expected {
		t.Errorf("First call result = %v, want %v", result1, expected)
	}

	// 检查缓存是否被填充
	cacheKey := reflect.TypeOf(input).String() + "_" + tagName
	structFieldsCacheMutex.RLock()
	_, exists := structFieldsCache[cacheKey]
	structFieldsCacheMutex.RUnlock()
	if !exists {
		t.Error("Cache should be populated after first call")
	}

	// 第二次调用应该从缓存获取
	result2, err2 := GetStructFieldsStrByTag(input, tagName)
	if err2 != nil {
		t.Errorf("Second call error = %v, wantErr false", err2)
		return
	}
	if result2 != expected {
		t.Errorf("Second call result = %v, want %v", result2, expected)
	}
	if result1 != result2 {
		t.Errorf("Cache results should be identical: %v != %v", result1, result2)
	}
}

// TestGetStructFieldsStrByTag_Concurrent 测试并发安全性
func TestGetStructFieldsStrByTag_Concurrent(t *testing.T) {
	// 清空缓存
	structFieldsCacheMutex.Lock()
	originalCache := structFieldsCache
	structFieldsCache = make(map[string][]string)
	structFieldsCacheMutex.Unlock()

	// 测试结束后恢复原缓存
	defer func() {
		structFieldsCacheMutex.Lock()
		structFieldsCache = originalCache
		structFieldsCacheMutex.Unlock()
	}()

	input := TestStructAllFields{}
	tagName := "json"
	expected := "name,age,email"

	const numGoroutines = 100
	var wg sync.WaitGroup
	results := make([]string, numGoroutines)
	errors := make([]error, numGoroutines)

	// 启动多个协程并发调用
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()
			result, err := GetStructFieldsStrByTag(input, tagName)
			results[index] = result
			errors[index] = err
		}(i)
	}

	wg.Wait()

	// 验证所有结果都是一致的
	for i := 0; i < numGoroutines; i++ {
		if errors[i] != nil {
			t.Errorf("Goroutine %d error = %v, wantErr false", i, errors[i])
		}
		if results[i] != expected {
			t.Errorf("Goroutine %d result = %v, want %v", i, results[i], expected)
		}
	}
}

// TestGetStructFieldsStrByTag_ComplexStruct 测试复杂结构体
func TestGetStructFieldsStrByTag_ComplexStruct(t *testing.T) {
	type ComplexStruct struct {
		// 基本类型
		StringField string `json:"string_field" db:"str_field"`
		IntField    int    `json:"int_field"`
		BoolField   bool   `db:"bool_field"`

		// 指针类型
		PtrField *string `json:"ptr_field"`

		// 切片和映射
		SliceField []int          `json:"slice_field"`
		MapField   map[string]int `json:"map_field"`

		// 嵌套结构体
		NestedField TestStructAllFields `json:"nested_field"`

		// 没有标签的字段
		NoTagField string

		// 标签值为 "-" 的字段
		IgnoredField string `json:"-"`

		// 空标签值的字段
		EmptyTagField string `json:""`
	}

	tests := []struct {
		name     string
		tagName  string
		expected string
	}{
		{
			name:     "json标签",
			tagName:  "json",
			expected: "string_field,int_field,ptr_field,slice_field,map_field,nested_field",
		},
		{
			name:     "db标签",
			tagName:  "db",
			expected: "str_field,bool_field",
		},
		{
			name:     "不存在的标签",
			tagName:  "xml",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetStructFieldsStrByTag(ComplexStruct{}, tt.tagName)
			if err != nil {
				t.Errorf("GetStructFieldsStrByTag() error = %v, wantErr false", err)
				return
			}
			if result != tt.expected {
				t.Errorf("GetStructFieldsStrByTag() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestGetStructFieldsStrByTag_EdgeCases 测试边界情况
func TestGetStructFieldsStrByTag_EdgeCases(t *testing.T) {
	// 测试只有一个字段的结构体
	type SingleFieldStruct struct {
		OnlyField string `json:"only"`
	}

	result, err := GetStructFieldsStrByTag(SingleFieldStruct{}, "json")
	if err != nil {
		t.Errorf("Single field struct error = %v, wantErr false", err)
		return
	}
	if result != "only" {
		t.Errorf("Single field struct result = %v, want 'only'", result)
	}

	// 测试字段名包含特殊字符的情况
	type SpecialTagStruct struct {
		Field1 string `json:"field-with-dash"`
		Field2 string `json:"field_with_underscore"`
		Field3 string `json:"field.with.dot"`
		Field4 string `json:"field,with,comma"`
	}

	result, err = GetStructFieldsStrByTag(SpecialTagStruct{}, "json")
	if err != nil {
		t.Errorf("Special tag struct error = %v, wantErr false", err)
		return
	}
	expected := "field-with-dash,field_with_underscore,field.with.dot,field,with,comma"
	if result != expected {
		t.Errorf("Special tag struct result = %v, want %v", result, expected)
	}
}

// TestGetStructFieldsStrByTag_MultiplePointerLevels 测试多级指针
func TestGetStructFieldsStrByTag_MultiplePointerLevels(t *testing.T) {
	input := TestStructAllFields{}
	ptr1 := &input
	ptr2 := &ptr1

	// 测试二级指针 - 当前实现不支持多级指针，应该返回错误
	result, err := GetStructFieldsStrByTag(ptr2, "json")
	if err == nil {
		t.Errorf("Double pointer should return error, but got result: %v", result)
		return
	}
	expectedErr := "参数必须是结构体类型"
	if err.Error() != expectedErr {
		t.Errorf("Double pointer error = %v, want %v", err.Error(), expectedErr)
	}
	if result != "" {
		t.Errorf("Double pointer result should be empty, got %v", result)
	}
}

// TestGetStructFieldsStrByTag_DifferentTagFormats 测试不同标签格式
func TestGetStructFieldsStrByTag_DifferentTagFormats(t *testing.T) {
	type TagFormatStruct struct {
		Field1 string `json:"field1,omitempty"`
		Field2 string `json:"field2,string"`
		Field3 string `json:"field3,omitempty,string"`
		Field4 string `json:",omitempty"`
		Field5 string `json:",string"`
	}

	result, err := GetStructFieldsStrByTag(TagFormatStruct{}, "json")
	if err != nil {
		t.Errorf("Tag format struct error = %v, wantErr false", err)
		return
	}
	// 注意：Go的Tag.Get()方法返回整个标签值，包括选项
	expected := "field1,omitempty,field2,string,field3,omitempty,string,,omitempty,,string"
	if result != expected {
		t.Errorf("Tag format struct result = %v, want %v", result, expected)
	}
}

// BenchmarkGetStructFieldsStrByTag 性能基准测试
func BenchmarkGetStructFieldsStrByTag(b *testing.B) {
	input := TestStructAllFields{}
	tagName := "json"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = GetStructFieldsStrByTag(input, tagName)
	}
}

// BenchmarkGetStructFieldsStrByTag_Cached 缓存性能基准测试
func BenchmarkGetStructFieldsStrByTag_Cached(b *testing.B) {
	input := TestStructAllFields{}
	tagName := "json"

	// 预热缓存
	_, _ = GetStructFieldsStrByTag(input, tagName)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = GetStructFieldsStrByTag(input, tagName)
	}
}
