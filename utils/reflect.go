package utils

import (
	"errors"
	"reflect"
	"strings"
	"sync"
)

var (
	// 通用结构体字段标签缓存，key 格式："{完整类型名}_{标签名}"
	structFieldsCache = make(map[string][]string)
	// 缓存读写锁，保证线程安全
	structFieldsCacheMutex sync.RWMutex
)

// GetStructFieldsByTag 通用函数：通过反射获取任意结构体的指定标签值
// 参数：
//   - structType: 结构体类型（可以是结构体实例、结构体指针或结构体类型）
//   - tagName: 要获取的标签名称（如 "mapstructure"、"json"、"db" 等）
//
// 返回：
//   - string: 用逗号分隔的字段标签值字符串
//   - error: 错误信息，如果参数无效或处理失败
//
// 特性：
//   - 支持任意结构体类型
//   - 支持任意标签名称
//   - 内置缓存机制，提高性能
//   - 线程安全
//   - 自动跳过匿名字段和无标签字段
func GetStructFieldsByTag(structType interface{}, tagName string) ([]string, error) {
	// 参数验证
	if structType == nil {
		return nil, errors.New("结构体类型不能为空")
	}
	if tagName == "" {
		return nil, errors.New("标签名称不能为空")
	}

	// 获取反射类型
	reflectType := reflect.TypeOf(structType)

	// 如果是指针类型，获取其指向的类型
	if reflectType.Kind() == reflect.Ptr {
		reflectType = reflectType.Elem()
	}

	// 检查是否为结构体类型
	if reflectType.Kind() != reflect.Struct {
		return nil, errors.New("参数必须是结构体类型")
	}

	// 生成缓存键：完整类型名 + "_" + 标签名
	cacheKey := reflectType.String() + "_" + tagName

	// 先尝试从缓存读取（使用读锁）
	structFieldsCacheMutex.RLock()
	if cachedResult, exists := structFieldsCache[cacheKey]; exists {
		structFieldsCacheMutex.RUnlock()
		return cachedResult, nil
	}
	structFieldsCacheMutex.RUnlock()

	// 缓存不存在，需要通过反射获取（使用写锁）
	structFieldsCacheMutex.Lock()
	defer structFieldsCacheMutex.Unlock()

	// 双重检查：可能在等待写锁期间，其他协程已经填充了缓存
	if cachedResult, exists := structFieldsCache[cacheKey]; exists {
		return cachedResult, nil
	}

	// 通过反射获取字段标签
	var fields []string
	for i := 0; i < reflectType.NumField(); i++ {
		field := reflectType.Field(i)

		// 跳过匿名字段
		if field.Anonymous {
			continue
		}

		// 获取指定标签的值
		if tagValue := field.Tag.Get(tagName); tagValue != "" && tagValue != "-" {
			fields = append(fields, tagValue)
		}
	}

	// 存入缓存
	structFieldsCache[cacheKey] = fields

	return fields, nil
}

func GetStructFieldsStrByTag(structType interface{}, tagName string) (string, error) {
	fields, err := GetStructFieldsByTag(structType, tagName)
	if err != nil {
		return "", err
	}
	return strings.Join(fields, ","), nil
}
